"""
簡化版技術指標計算
不依賴 TA-Lib，使用 pandas 和 numpy 實現
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
from langchain_core.tools import tool

# 導入基礎 API
from .binance_api import (
    get_kline_1m, get_kline_5m, get_kline_15m, 
    get_kline_1h, get_kline_4h, get_kline_1d,
    get_default_symbol
)


def get_kline_data_simple(timeframe: str, symbol: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray, np.ndarray, int]:
    """獲取 K 線數據（簡化版）"""
    import time

    if symbol is None:
        symbol = get_default_symbol()

    print(f"📈 正在獲取 {timeframe} K線數據，交易對: {symbol}")

    # 時間框架映射
    timeframe_map = {
        '1m': get_kline_1m,
        '5m': get_kline_5m,
        '15m': get_kline_15m,
        '1h': get_kline_1h,
        '4h': get_kline_4h,
        '1d': get_kline_1d
    }

    # 數據數量映射 - 為5m減少數據需求量
    count_map = {
        '1m': 30,
        '5m': 60,   # 從120減少到60，降低數據需求
        '15m': 80,  # 從120減少到80
        '1h': 50,
        '4h': 20,
        '1d': 7
    }

    get_kline_func = timeframe_map.get(timeframe)
    if not get_kline_func:
        print(f"❌ 不支持的時間框架: {timeframe}")
        return np.array([]), np.array([]), np.array([]), 0

    count = count_map.get(timeframe, 30)

    # 添加重試機制
    for retry in range(3):
        try:
            print(f"🔄 獲取K線數據 (嘗試 {retry + 1}/3)")
            kline_info = get_kline_func(symbol)

            if not kline_info:
                raise ValueError(f"API返回空的K線數據")

            if len(kline_info) < 14:  # 至少需要14個數據點來計算RSI等指標
                raise ValueError(f"K線數據不足，僅有 {len(kline_info)} 個數據點，至少需要14個")

            # 取最近的數據，但不超過可用數據量
            actual_count = min(count, len(kline_info))
            kline_info = kline_info[-actual_count:]

            print(f"📊 成功獲取 {len(kline_info)} 個 {timeframe} K線數據點")

            # 提取價格數據並驗證
            closing_prices = []
            high_prices = []
            low_prices = []

            for i, kline in enumerate(kline_info):
                try:
                    close = float(kline[4])
                    high = float(kline[2])
                    low = float(kline[3])

                    # 基本數據驗證
                    if close <= 0 or high <= 0 or low <= 0:
                        print(f"⚠️ 第 {i} 個數據點價格異常: close={close}, high={high}, low={low}")
                        continue

                    if high < low:
                        print(f"⚠️ 第 {i} 個數據點高價低於低價: high={high}, low={low}")
                        continue

                    closing_prices.append(close)
                    high_prices.append(high)
                    low_prices.append(low)

                except (ValueError, IndexError) as e:
                    print(f"⚠️ 第 {i} 個數據點格式錯誤: {e}")
                    continue

            if len(closing_prices) < 14:
                raise ValueError(f"有效數據點不足，僅有 {len(closing_prices)} 個，至少需要14個")

            closing_prices = np.array(closing_prices)
            high_prices = np.array(high_prices)
            low_prices = np.array(low_prices)

            print(f"✅ {timeframe} K線數據處理完成，有效數據點: {len(closing_prices)}")
            return closing_prices, high_prices, low_prices, len(closing_prices)

        except Exception as e:
            error_msg = f"獲取K線數據失敗 (嘗試 {retry + 1}/3): {str(e)}"
            print(f"❌ {error_msg}")

            if retry < 2:  # 不是最後一次重試
                wait_time = (retry + 1) * 2
                print(f"⏳ 等待 {wait_time} 秒後重試...")
                time.sleep(wait_time)
            else:
                print(f"💥 {timeframe} K線數據獲取最終失敗")

    # 所有重試都失敗，返回空數據
    return np.array([]), np.array([]), np.array([]), 0


def calculate_sma(prices: np.ndarray, period: int = 14) -> np.ndarray:
    """計算簡單移動平均線"""
    if len(prices) < period:
        return np.full(len(prices), np.nan)
    
    sma = np.full(len(prices), np.nan)
    for i in range(period - 1, len(prices)):
        sma[i] = np.mean(prices[i - period + 1:i + 1])
    
    return sma


def calculate_ema(prices: np.ndarray, period: int = 14) -> np.ndarray:
    """計算指數移動平均線"""
    if len(prices) == 0:
        return np.array([])
    
    ema = np.full(len(prices), np.nan)
    multiplier = 2 / (period + 1)
    
    # 第一個值使用 SMA
    if len(prices) >= period:
        ema[period - 1] = np.mean(prices[:period])
        
        # 計算後續的 EMA
        for i in range(period, len(prices)):
            ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier))
    
    return ema


def calculate_rsi(prices: np.ndarray, period: int = 14) -> np.ndarray:
    """計算相對強弱指數"""
    if len(prices) < period + 1:
        return np.full(len(prices), np.nan)
    
    # 計算價格變化
    deltas = np.diff(prices)
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)
    
    # 計算平均收益和損失
    avg_gains = np.full(len(prices), np.nan)
    avg_losses = np.full(len(prices), np.nan)
    
    # 第一個週期使用簡單平均
    if len(gains) >= period:
        avg_gains[period] = np.mean(gains[:period])
        avg_losses[period] = np.mean(losses[:period])
        
        # 後續使用指數平滑
        for i in range(period + 1, len(prices)):
            avg_gains[i] = (avg_gains[i-1] * (period - 1) + gains[i-1]) / period
            avg_losses[i] = (avg_losses[i-1] * (period - 1) + losses[i-1]) / period
    
    # 計算 RSI
    rsi = np.full(len(prices), np.nan)
    for i in range(period, len(prices)):
        if avg_losses[i] != 0:
            rs = avg_gains[i] / avg_losses[i]
            rsi[i] = 100 - (100 / (1 + rs))
        else:
            rsi[i] = 100
    
    return rsi


def calculate_macd(prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """計算 MACD"""
    if len(prices) < slow:
        empty = np.full(len(prices), np.nan)
        return empty, empty, empty
    
    # 計算快線和慢線 EMA
    ema_fast = calculate_ema(prices, fast)
    ema_slow = calculate_ema(prices, slow)
    
    # 計算 MACD 線
    macd_line = ema_fast - ema_slow
    
    # 計算信號線
    signal_line = calculate_ema(macd_line[~np.isnan(macd_line)], signal)
    
    # 調整信號線長度
    signal_full = np.full(len(prices), np.nan)
    valid_start = slow - 1
    if len(signal_line) > 0:
        signal_full[valid_start:valid_start + len(signal_line)] = signal_line
    
    # 計算直方圖
    histogram = macd_line - signal_full
    
    return macd_line, signal_full, histogram


def calculate_atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
    """計算真實波幅"""
    if len(high) < 2:
        return np.full(len(high), np.nan)
    
    # 計算真實範圍
    tr1 = high - low
    tr2 = np.abs(high - np.roll(close, 1))
    tr3 = np.abs(low - np.roll(close, 1))
    
    # 第一個值設為 NaN
    tr2[0] = np.nan
    tr3[0] = np.nan
    
    # 取最大值
    true_range = np.nanmax([tr1, tr2, tr3], axis=0)
    
    # 計算 ATR（使用 SMA）
    atr = calculate_sma(true_range, period)
    
    return atr


def calculate_kdj(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 9) -> Tuple[np.ndarray, np.ndarray]:
    """計算 KDJ 指標"""
    if len(high) < period:
        empty = np.full(len(high), np.nan)
        return empty, empty
    
    # 計算 RSV
    rsv = np.full(len(high), np.nan)
    for i in range(period - 1, len(high)):
        highest = np.max(high[i - period + 1:i + 1])
        lowest = np.min(low[i - period + 1:i + 1])
        if highest != lowest:
            rsv[i] = (close[i] - lowest) / (highest - lowest) * 100
        else:
            rsv[i] = 50
    
    # 計算 K 和 D
    k = np.full(len(high), np.nan)
    d = np.full(len(high), np.nan)
    
    # 初始值
    k[period - 1] = rsv[period - 1]
    d[period - 1] = k[period - 1]
    
    # 計算後續值
    for i in range(period, len(high)):
        k[i] = (2/3) * k[i-1] + (1/3) * rsv[i]
        d[i] = (2/3) * d[i-1] + (1/3) * k[i]
    
    return k, d


def get_simple_technical_indicators_raw(timeframe: str, symbol: Optional[str] = None) -> Dict:
    """獲取簡化版技術指標
    
    Args:
        timeframe: 時間框架 ('1m', '5m', '15m', '1h', '4h', '1d')
        symbol: 交易對符號
    
    Returns:
        包含技術指標的字典
    """
    if symbol is None:
        symbol = get_default_symbol()
    
    print(f"🧮 開始計算 {timeframe} 技術指標")

    try:
        # 獲取價格數據
        closing_prices, high_prices, low_prices, count = get_kline_data_simple(timeframe, symbol)

        if count == 0:
            error_msg = f'無法獲取 {timeframe} 的價格數據'
            print(f"❌ {error_msg}")
            return {
                'error': error_msg,
                'timeframe': timeframe,
                'symbol': symbol
            }

        print(f"📊 開始計算技術指標，數據點數: {count}")

        # 初始化指標字典
        indicators = {
            'timeframe': timeframe,
            'symbol': symbol,
            'data_count': count,
        }

        # 逐個計算技術指標，並處理可能的錯誤
        try:
            print("📈 計算ATR...")
            atr = calculate_atr(high_prices, low_prices, closing_prices)
            indicators['atr'] = atr.tolist() if len(atr) > 0 else []
        except Exception as e:
            print(f"⚠️ ATR計算失敗: {e}")
            indicators['atr'] = []

        try:
            print("📈 計算移動平均線...")
            indicators['ma_7'] = calculate_sma(closing_prices, 7).tolist()
            indicators['ma_14'] = calculate_sma(closing_prices, 14).tolist()
            indicators['ema_14'] = calculate_ema(closing_prices, 14).tolist()
        except Exception as e:
            print(f"⚠️ 移動平均線計算失敗: {e}")
            indicators['ma_7'] = []
            indicators['ma_14'] = []
            indicators['ema_14'] = []

        try:
            print("📈 計算RSI...")
            indicators['rsi_6'] = calculate_rsi(closing_prices, 6).tolist()
            indicators['rsi_12'] = calculate_rsi(closing_prices, 12).tolist()
            indicators['rsi_24'] = calculate_rsi(closing_prices, 24).tolist()
        except Exception as e:
            print(f"⚠️ RSI計算失敗: {e}")
            indicators['rsi_6'] = []
            indicators['rsi_12'] = []
            indicators['rsi_24'] = []

        try:
            print("📈 計算MACD...")
            macd, macd_signal, macd_hist = calculate_macd(closing_prices)
            indicators.update({
                'macd': macd.tolist() if len(macd) > 0 else [],
                'macd_signal': macd_signal.tolist() if len(macd_signal) > 0 else [],
                'macd_hist': macd_hist.tolist() if len(macd_hist) > 0 else [],
            })
        except Exception as e:
            print(f"⚠️ MACD計算失敗: {e}")
            indicators.update({
                'macd': [],
                'macd_signal': [],
                'macd_hist': [],
            })

        try:
            print("📈 計算KDJ...")
            k, d = calculate_kdj(high_prices, low_prices, closing_prices)
            indicators.update({
                'kdj_k': k.tolist() if len(k) > 0 else [],
                'kdj_d': d.tolist() if len(d) > 0 else [],
            })
        except Exception as e:
            print(f"⚠️ KDJ計算失敗: {e}")
            indicators.update({
                'kdj_k': [],
                'kdj_d': [],
            })

        # 檢查關鍵指標是否計算成功
        key_indicators = ['rsi_12', 'ma_7', 'ma_14', 'macd_hist']
        successful_indicators = [ind for ind in key_indicators if indicators.get(ind)]

        if len(successful_indicators) < 2:
            print(f"⚠️ 關鍵指標計算成功數量不足: {successful_indicators}")

        print(f"✅ {timeframe} 技術指標計算完成，成功指標: {len([k for k, v in indicators.items() if isinstance(v, list) and v])}")
        return indicators

    except Exception as e:
        error_msg = f'計算技術指標失敗: {str(e)}'
        print(f"❌ {error_msg}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return {
            'error': error_msg,
            'timeframe': timeframe,
            'symbol': symbol
        }


@tool
def get_simple_technical_indicators(timeframe: str, symbol: Optional[str] = None) -> Dict:
    """獲取簡化版技術指標 (LangChain 工具版本)

    Args:
        timeframe: 時間框架 ('1m', '5m', '15m', '1h', '4h', '1d')
        symbol: 交易對符號

    Returns:
        包含技術指標的字典
    """
    return get_simple_technical_indicators_raw(timeframe, symbol)
