"""
高級 LangChain 交易代理
充分利用 LangChain 的記憶、對話歷史、工具鏈等功能
實現連貫的交易策略和決策追蹤
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import os
import numpy as np
from pathlib import Path

# 嘗試導入 pandas，如果失敗則使用替代方案
try:
    import pandas as pd
except ImportError:
    # 創建一個簡單的 pandas 替代
    class MockPandas:
        @staticmethod
        def isna(value):
            return value is None or (isinstance(value, float) and np.isnan(value))
    pd = MockPandas()

# LangChain 核心組件
from langchain.memory import ConversationBufferMemory, ConversationSummaryBufferMemory
from langchain.schema import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.tools import tool
from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.callbacks import StdOutCallbackHandler
from langchain.schema.runnable import RunnablePassthrough
from langchain.tools import BaseTool

# 導入現有的工具
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.binance_api import (
    get_price, get_open_futures_orders, get_futures_order_history,
    modify_stop_loss_take_profit, close_position_immediately, set_default_symbol
)
from tools.enhanced_trading_tools import (
    enhanced_get_market_data, enhanced_get_technical_indicators,
    enhanced_execute_buy_order, enhanced_execute_sell_order
)
from agents.advanced_prompts import AdvancedPromptTemplates, PromptContextBuilder


class TradingMemoryManager:
    """交易記憶管理器 - 管理交易決策的歷史和上下文"""
    
    def __init__(self, memory_file: str = "trading_memory.json"):
        self.memory_file = memory_file
        self.trading_sessions = []
        self.decision_history = []
        self.strategy_context = {}
        self.load_memory()
    
    def load_memory(self):
        """從文件加載記憶"""
        if os.path.exists(self.memory_file):
            try:
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.trading_sessions = data.get('trading_sessions', [])
                    self.decision_history = data.get('decision_history', [])
                    self.strategy_context = data.get('strategy_context', {})
            except Exception as e:
                print(f"載入記憶文件失敗: {e}")
    
    def save_memory(self):
        """保存記憶到文件"""
        try:
            data = {
                'trading_sessions': self.trading_sessions,
                'decision_history': self.decision_history,
                'strategy_context': self.strategy_context,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存記憶文件失敗: {e}")
    
    def add_decision(self, decision_type: str, reasoning: str, market_context: Dict, result: str):
        """添加決策記錄"""
        decision = {
            'timestamp': datetime.now().isoformat(),
            'decision_type': decision_type,
            'reasoning': reasoning,
            'market_context': market_context,
            'result': result
        }
        self.decision_history.append(decision)
        
        # 只保留最近50個決策
        if len(self.decision_history) > 50:
            self.decision_history = self.decision_history[-50:]
        
        self.save_memory()
    
    def get_recent_decisions(self, count: int = 5) -> List[Dict]:
        """獲取最近的決策記錄"""
        return self.decision_history[-count:] if self.decision_history else []
    
    def update_strategy_context(self, key: str, value: Any):
        """更新策略上下文"""
        self.strategy_context[key] = value
        self.save_memory()
    
    def get_strategy_summary(self) -> str:
        """獲取策略總結"""
        recent_decisions = self.get_recent_decisions(10)
        if not recent_decisions:
            return "無歷史決策記錄"
        
        summary = "最近交易決策總結:\n"
        for i, decision in enumerate(recent_decisions[-5:], 1):
            summary += f"{i}. {decision['timestamp'][:16]} - {decision['decision_type']}: {decision['reasoning'][:100]}...\n"
        
        return summary


class AdvancedTradingAgent:
    """高級交易代理 - 充分利用 LangChain 功能"""
    
    def __init__(self, 
                 model_name: str = "gemini-2.5-flash-lite-preview-06-17",
                 symbol: str = 'ETHUSDT',
                 memory_type: str = "buffer_summary"):
        
        self.symbol = symbol
        set_default_symbol(symbol)
        
        # 初始化 LLM
        self.llm = ChatGoogleGenerativeAI(
            model=model_name, 
            temperature=0.1,  # 稍微增加創造性但保持一致性
            max_tokens=2048
        )
        
        # 初始化記憶管理器
        self.memory_manager = TradingMemoryManager(f"trading_memory_{symbol}.json")
        
        # 初始化 LangChain 記憶系統
        self.conversation_memory = self._setup_memory(memory_type)
        
        # 設置工具
        self.tools = self._setup_tools()
        
        # 創建代理
        self.agent = self._create_agent()
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            memory=self.conversation_memory,
            verbose=True,
            max_iterations=10,
            early_stopping_method="generate",
            handle_parsing_errors=True
        )
    
    def _setup_memory(self, memory_type: str):
        """設置記憶系統"""
        if memory_type == "buffer":
            return ConversationBufferMemory(
                memory_key="chat_history",
                return_messages=True,
                output_key="output"
            )
        elif memory_type == "buffer_summary":
            return ConversationSummaryBufferMemory(
                llm=self.llm,
                memory_key="chat_history",
                return_messages=True,
                output_key="output",
                max_token_limit=1000
            )
        else:
            return ConversationBufferMemory(
                memory_key="chat_history",
                return_messages=True,
                output_key="output"
            )
    
    def _setup_tools(self) -> List[BaseTool]:
        """設置工具列表"""
        return [
            enhanced_get_market_data,
            enhanced_get_technical_indicators,
            get_open_futures_orders,
            get_futures_order_history,
            enhanced_execute_buy_order,
            enhanced_execute_sell_order,
            modify_stop_loss_take_profit,
            close_position_immediately,
            self._create_memory_tool(),
            self._create_strategy_analysis_tool()
        ]
    
    def _create_memory_tool(self):
        """創建記憶工具"""
        @tool
        def access_trading_memory(query_type: str = "recent_decisions") -> str:
            """訪問交易記憶和歷史決策
            
            Args:
                query_type: 查詢類型 - 'recent_decisions', 'strategy_summary', 'context'
            
            Returns:
                相關的記憶信息
            """
            if query_type == "recent_decisions":
                decisions = self.memory_manager.get_recent_decisions(5)
                if not decisions:
                    return "無最近決策記錄"
                
                result = "最近5個交易決策:\n"
                for i, decision in enumerate(decisions, 1):
                    result += f"{i}. {decision['timestamp'][:16]} - {decision['decision_type']}\n"
                    result += f"   推理: {decision['reasoning'][:150]}...\n"
                    result += f"   結果: {decision['result'][:100]}...\n\n"
                return result
            
            elif query_type == "strategy_summary":
                return self.memory_manager.get_strategy_summary()
            
            elif query_type == "context":
                return json.dumps(self.memory_manager.strategy_context, ensure_ascii=False, indent=2)
            
            return "未知的查詢類型"
        
        return access_trading_memory
    
    def _create_strategy_analysis_tool(self):
        """創建策略分析工具"""
        @tool
        def analyze_market_trend(timeframe_list: str = "5m,15m,1h,4h") -> str:
            """分析多時間框架的市場趋势

            Args:
                timeframe_list: 要分析的時間框架，用逗號分隔，如 "5m,15m,1h,4h"

            Returns:
                市場趋势分析結果
            """
            import time
            import traceback

            # 解析時間框架列表
            timeframes = [tf.strip() for tf in timeframe_list.split(',')]
            analysis = "多時間框架趋势分析:\n"

            for tf in timeframes:
                success = False
                last_error = None

                # 對每個時間框架進行3次重試
                for retry_count in range(3):
                    try:
                        print(f"🔍 正在分析 {tf} 時間框架 (嘗試 {retry_count + 1}/3)")

                        # 直接調用工具函數
                        indicators_result = enhanced_get_technical_indicators(timeframe=tf, symbol=self.symbol)

                        # 檢查返回結果
                        if isinstance(indicators_result, dict) and 'error' in indicators_result:
                            raise Exception(f"指標計算錯誤: {indicators_result['error']}")

                        indicators = indicators_result.get('indicators', {}) if isinstance(indicators_result, dict) else {}

                        # 檢查指標數據是否有效
                        if not indicators:
                            raise Exception("未獲取到有效的技術指標數據")

                        # 簡化的趋势判斷
                        rsi = indicators.get('rsi_12', [])
                        macd_hist = indicators.get('macd_hist', [])
                        ma_7 = indicators.get('ma_7', [])
                        ma_14 = indicators.get('ma_14', [])

                        if rsi and macd_hist and ma_7 and ma_14:
                            # 獲取最新值，並處理 NaN
                            current_rsi = rsi[-1] if rsi and len(rsi) > 0 and not pd.isna(rsi[-1]) else 50
                            current_macd_hist = macd_hist[-1] if macd_hist and len(macd_hist) > 0 and not pd.isna(macd_hist[-1]) else 0
                            current_ma7 = ma_7[-1] if ma_7 and len(ma_7) > 0 and not pd.isna(ma_7[-1]) else 0
                            current_ma14 = ma_14[-1] if ma_14 and len(ma_14) > 0 and not pd.isna(ma_14[-1]) else 0

                            # 趨勢判斷邏輯
                            trend = "中性"
                            momentum = "中性"

                            # RSI 動量判斷
                            if current_rsi > 70:
                                momentum = "超買"
                            elif current_rsi < 30:
                                momentum = "超賣"
                            elif current_rsi > 60:
                                momentum = "偏強"
                            elif current_rsi < 40:
                                momentum = "偏弱"

                            # 趨勢判斷（結合MA和MACD）
                            if current_ma7 > current_ma14 and current_macd_hist > 0:
                                trend = "看漲"
                            elif current_ma7 < current_ma14 and current_macd_hist < 0:
                                trend = "看跌"
                            elif current_ma7 > current_ma14:
                                trend = "偏多"
                            elif current_ma7 < current_ma14:
                                trend = "偏空"

                            analysis += f"{tf}: RSI={current_rsi:.1f}({momentum}), MACD柱={current_macd_hist:.4f}, 趋势={trend}\n"
                            success = True
                            break
                        else:
                            raise Exception("關鍵技術指標數據缺失或無效")

                    except Exception as e:
                        last_error = e
                        error_msg = str(e)
                        print(f"❌ {tf} 時間框架分析失敗 (嘗試 {retry_count + 1}/3): {error_msg}")

                        # 如果是數據獲取問題，等待後重試
                        if retry_count < 2:  # 不是最後一次重試
                            if "API" in error_msg or "網絡" in error_msg or "timeout" in error_msg.lower():
                                print(f"⏳ 等待 {(retry_count + 1) * 2} 秒後重試...")
                                time.sleep((retry_count + 1) * 2)  # 遞增等待時間
                            else:
                                time.sleep(1)  # 其他錯誤等待1秒

                # 如果所有重試都失敗了
                if not success:
                    detailed_error = f"連續3次重試失敗，最後錯誤: {str(last_error)}"
                    analysis += f"{tf}: 分析失敗 - {detailed_error}\n"
                    print(f"💥 {tf} 時間框架最終分析失敗: {detailed_error}")

            return analysis

        return analyze_market_trend

    def _create_agent(self):
        """創建高級 LangChain 代理"""
        # 使用高級提示模板
        system_template = AdvancedPromptTemplates.get_system_prompt_template()

        # 創建提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", system_template),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])

        return create_tool_calling_agent(self.llm, self.tools, prompt)

    def execute_trading_analysis(self, analysis_type: str = "new_order") -> str:
        """執行交易分析"""
        try:
            if analysis_type == "new_order":
                input_message = self._create_new_order_context()
            elif analysis_type == "position_management":
                input_message = self._create_position_management_context()
            else:
                input_message = "請分析當前市場狀況並提供交易建議"

            # 執行代理
            result = self.agent_executor.invoke({
                "input": input_message
            })

            # 記錄決策
            self.memory_manager.add_decision(
                decision_type=analysis_type,
                reasoning=input_message[:200],
                market_context={"timestamp": datetime.now().isoformat()},
                result=result["output"][:500]
            )

            return result["output"]

        except Exception as e:
            error_msg = f"執行交易分析時發生錯誤：{str(e)}"
            print(error_msg)
            return error_msg

    def _create_new_order_context(self) -> str:
        """創建新訂單分析上下文"""
        context = PromptContextBuilder.build_trading_context(
            symbol=self.symbol,
            analysis_type="new_order"
        )

        # 獲取當前價格
        try:
            market_data = enhanced_get_market_data(self.symbol)
            current_price = market_data.get('price_info', {}).get('price', 'N/A')
            context['current_price'] = current_price
        except:
            context['current_price'] = 'N/A'

        return AdvancedPromptTemplates.get_new_order_analysis_template().format(**context)

    def _create_position_management_context(self) -> str:
        """創建持倉管理上下文"""
        context = PromptContextBuilder.build_trading_context(
            symbol=self.symbol,
            analysis_type="position_management"
        )

        return AdvancedPromptTemplates.get_position_management_template().format(**context)

    def get_conversation_history(self) -> List[BaseMessage]:
        """獲取對話歷史"""
        if hasattr(self.conversation_memory, 'chat_memory'):
            return self.conversation_memory.chat_memory.messages
        return []

    def clear_conversation_history(self):
        """清除對話歷史"""
        if hasattr(self.conversation_memory, 'clear'):
            self.conversation_memory.clear()

    def get_memory_summary(self) -> str:
        """獲取記憶總結"""
        return self.memory_manager.get_strategy_summary()


# 為了向後兼容，提供一個簡化的接口
class EnhancedLangchainAgent(AdvancedTradingAgent):
    """增強版 LangChain 代理 - 向後兼容接口"""

    def analyze_and_execute_new_order(self) -> str:
        """分析並執行新訂單決策"""
        return self.execute_trading_analysis("new_order")

    def analyze_and_manage_position(self) -> str:
        """分析並管理現有持倉"""
        return self.execute_trading_analysis("position_management")
