#!/usr/bin/env python3
"""
專門測試5分鐘時間框架修復
"""

import os
import sys
import traceback

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_5m_timeframe():
    """專門測試5分鐘時間框架"""
    print("🧪 開始測試5分鐘時間框架修復...")
    
    try:
        # 測試基礎K線數據獲取
        print("\n📈 測試基礎K線數據獲取...")
        from core.simple_indicators import get_kline_data_simple
        
        closing_prices, high_prices, low_prices, count = get_kline_data_simple('5m', 'ETHUSDT')
        
        if count > 0:
            print(f"✅ 成功獲取5分鐘K線數據: {count} 個數據點")
            print(f"   最新收盤價: {closing_prices[-1]:.2f}")
            print(f"   價格範圍: {low_prices.min():.2f} - {high_prices.max():.2f}")
        else:
            print("❌ 無法獲取5分鐘K線數據")
            return
        
        # 測試技術指標計算
        print("\n📊 測試技術指標計算...")
        from core.simple_indicators import get_simple_technical_indicators_raw
        
        indicators = get_simple_technical_indicators_raw('5m', 'ETHUSDT')
        
        if 'error' in indicators:
            print(f"❌ 技術指標計算失敗: {indicators['error']}")
        else:
            print("✅ 技術指標計算成功")
            
            # 檢查關鍵指標
            key_indicators = ['rsi_12', 'ma_7', 'ma_14', 'macd_hist']
            for ind in key_indicators:
                if ind in indicators and indicators[ind]:
                    latest_value = indicators[ind][-1] if indicators[ind] else 'N/A'
                    print(f"   {ind}: {latest_value}")
                else:
                    print(f"   {ind}: 缺失")
        
        # 測試增強版工具
        print("\n🔧 測試增強版技術指標工具...")
        from tools.enhanced_trading_tools import enhanced_get_technical_indicators

        # 直接調用函數而不是通過工具接口
        result = enhanced_get_technical_indicators.func('5m', 'ETHUSDT')
        
        if 'error' in result:
            print(f"❌ 增強版工具失敗: {result['error']}")
        else:
            print("✅ 增強版工具成功")
            indicators = result.get('indicators', {})
            analysis = result.get('analysis', {})
            
            print(f"   趨勢: {analysis.get('trend', 'N/A')}")
            print(f"   動量: {analysis.get('momentum', 'N/A')}")
            print(f"   信號: {analysis.get('signals', [])}")
        
        # 測試多時間框架分析中的5分鐘部分
        print("\n🤖 測試多時間框架分析工具中的5分鐘部分...")
        
        try:
            from agents.advanced_langchain_agent import AdvancedTradingAgent
            
            agent = AdvancedTradingAgent('ETHUSDT')
            
            # 直接測試分析工具的內部邏輯
            tools = agent._setup_tools()
            analyze_tool = None
            
            for tool in tools:
                if hasattr(tool, 'name') and 'analyze_market_trend' in tool.name:
                    analyze_tool = tool
                    break
            
            if analyze_tool:
                # 只測試5分鐘
                result = analyze_tool.invoke({"timeframe_list": "5m"})
                print("✅ 5分鐘多時間框架分析成功")
                print(f"結果: {result}")
            else:
                print("❌ 未找到分析工具")
                
        except Exception as e:
            print(f"❌ 多時間框架分析測試失敗: {str(e)}")
            print(f"詳細錯誤: {traceback.format_exc()}")
        
        print("\n🎯 測試完成！")
        
    except Exception as e:
        print(f"💥 測試過程中發生錯誤: {str(e)}")
        print(f"錯誤詳情: {traceback.format_exc()}")

if __name__ == "__main__":
    test_5m_timeframe()
