#!/usr/bin/env python3
"""
測試多時間框架分析修復
"""

import os
import sys
import traceback
from datetime import datetime

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_timeframe_analysis():
    """測試時間框架分析"""
    print("🧪 開始測試多時間框架分析修復...")
    
    try:
        # 導入必要的模組
        from tools.enhanced_trading_tools import enhanced_get_technical_indicators
        from agents.advanced_langchain_agent import AdvancedTradingAgent
        
        print("✅ 模組導入成功")
        
        # 測試各個時間框架的技術指標獲取
        timeframes = ['5m', '15m', '1h', '4h']
        symbol = 'ETHUSDT'
        
        print(f"\n📊 測試技術指標獲取 - 交易對: {symbol}")
        print("=" * 50)
        
        results = {}
        for tf in timeframes:
            print(f"\n🔍 測試 {tf} 時間框架...")
            try:
                result = enhanced_get_technical_indicators(timeframe=tf, symbol=symbol)
                
                if isinstance(result, dict):
                    if 'error' in result:
                        print(f"❌ {tf}: {result['error']}")
                        results[tf] = {'status': 'failed', 'error': result['error']}
                    else:
                        indicators = result.get('indicators', {})
                        key_indicators = ['rsi_12', 'ma_7', 'ma_14', 'macd_hist']
                        available_indicators = [ind for ind in key_indicators if indicators.get(ind)]
                        
                        print(f"✅ {tf}: 成功獲取 {len(available_indicators)}/{len(key_indicators)} 個關鍵指標")
                        print(f"   可用指標: {available_indicators}")
                        
                        results[tf] = {
                            'status': 'success', 
                            'indicators_count': len(available_indicators),
                            'available_indicators': available_indicators
                        }
                else:
                    print(f"❌ {tf}: 返回結果格式錯誤")
                    results[tf] = {'status': 'failed', 'error': '返回結果格式錯誤'}
                    
            except Exception as e:
                error_msg = f"異常錯誤: {str(e)}"
                print(f"💥 {tf}: {error_msg}")
                results[tf] = {'status': 'exception', 'error': error_msg}
        
        # 測試多時間框架分析工具
        print(f"\n🤖 測試多時間框架分析工具...")
        print("=" * 50)
        
        try:
            # 創建代理實例
            agent = AdvancedTradingAgent(symbol=symbol)
            
            # 獲取分析工具
            tools = agent._setup_tools()
            analyze_tool = None
            
            for tool in tools:
                if hasattr(tool, 'name') and 'analyze_market_trend' in tool.name:
                    analyze_tool = tool
                    break
            
            if analyze_tool:
                print("✅ 找到多時間框架分析工具")
                
                # 測試工具調用
                try:
                    analysis_result = analyze_tool.invoke({"timeframe_list": "5m,15m,1h,4h"})
                    print("✅ 多時間框架分析工具調用成功")
                    print(f"分析結果:\n{analysis_result}")
                    
                except Exception as e:
                    print(f"❌ 多時間框架分析工具調用失敗: {str(e)}")
                    print(f"錯誤詳情: {traceback.format_exc()}")
            else:
                print("❌ 未找到多時間框架分析工具")
                
        except Exception as e:
            print(f"💥 創建代理失敗: {str(e)}")
            print(f"錯誤詳情: {traceback.format_exc()}")
        
        # 總結測試結果
        print(f"\n📋 測試結果總結")
        print("=" * 50)
        
        success_count = sum(1 for r in results.values() if r['status'] == 'success')
        total_count = len(results)
        
        print(f"成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        for tf, result in results.items():
            status_emoji = "✅" if result['status'] == 'success' else "❌"
            print(f"{status_emoji} {tf}: {result['status']}")
            if result['status'] == 'success':
                print(f"   指標數量: {result['indicators_count']}")
            elif 'error' in result:
                print(f"   錯誤: {result['error'][:100]}...")
        
        # 建議
        print(f"\n💡 修復建議")
        print("=" * 50)
        
        if success_count == total_count:
            print("🎉 所有時間框架分析都成功！修復完成。")
        elif success_count > 0:
            failed_timeframes = [tf for tf, r in results.items() if r['status'] != 'success']
            print(f"⚠️ 仍有 {len(failed_timeframes)} 個時間框架失敗: {failed_timeframes}")
            print("建議檢查:")
            print("1. API 連接狀態")
            print("2. 數據獲取頻率限制")
            print("3. 特定時間框架的數據可用性")
        else:
            print("❌ 所有時間框架都失敗，建議檢查:")
            print("1. 網絡連接")
            print("2. API 密鑰配置")
            print("3. Binance API 服務狀態")
        
    except Exception as e:
        print(f"💥 測試過程中發生嚴重錯誤: {str(e)}")
        print(f"錯誤詳情: {traceback.format_exc()}")

if __name__ == "__main__":
    test_timeframe_analysis()
